{"$message_type":"diagnostic","message":"unused import: `Read`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/parse-snort.rs","byte_start":129,"byte_end":133,"line_start":3,"line_end":3,"column_start":35,"column_end":39,"is_primary":true,"text":[{"text":"use std::io::{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>};","highlight_start":35,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/bin/parse-snort.rs","byte_start":127,"byte_end":133,"line_start":3,"line_end":3,"column_start":33,"column_end":39,"is_primary":true,"text":[{"text":"use std::io::{BufRead, BufReader, Read};","highlight_start":33,"highlight_end":39}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `Read`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/parse-snort.rs:3:35\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::io::{BufRead, BufReader, Read};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unreachable statement","code":{"code":"unreachable_code","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/parse-snort.rs","byte_start":13122,"byte_end":16856,"line_start":412,"line_end":504,"column_start":5,"column_end":6,"is_primary":true,"text":[{"text":"    for entry in sensor_names_and_instances.iter() {","highlight_start":5,"highlight_end":53},{"text":"        let sensor_name = entry.key();","highlight_start":1,"highlight_end":39},{"text":"        let instance = entry.value();","highlight_start":1,"highlight_end":38},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        let dest_dir          = format!(\"{}/{}/{}\", SnortConfig::get_logs_parsed_dir(), sensor_name, args.log_date);","highlight_start":1,"highlight_end":117},{"text":"        let accomplished_file = format!(\"{}/{}-accomplished.log\", dest_dir, args.log_date);","highlight_start":1,"highlight_end":92},{"text":"        let log_file          = format!(\"{}/{}.log\", dest_dir, args.log_date);","highlight_start":1,"highlight_end":79},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        let database_name = create_name_of_database(&SnortConfig::SLUG.value_string(), &args.log_date, sensor_name);","highlight_start":1,"highlight_end":117},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        // ################################################","highlight_start":1,"highlight_end":60},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        // remove and/or create dest_dir","highlight_start":1,"highlight_end":41},{"text":"        if Path::new(&dest_dir).exists() {","highlight_start":1,"highlight_end":43},{"text":"            let mut should_rm_dest_dir = false;","highlight_start":1,"highlight_end":48},{"text":"","highlight_start":1,"highlight_end":1},{"text":"            if args.force {","highlight_start":1,"highlight_end":28},{"text":"                should_rm_dest_dir = true;","highlight_start":1,"highlight_end":43},{"text":"            } else {","highlight_start":1,"highlight_end":21},{"text":"                if Path::new(&accomplished_file).exists() {","highlight_start":1,"highlight_end":60},{"text":"                    println!(\"{} for sensor {} is already parsed. skipping\", args.log_date, sensor_name);","highlight_start":1,"highlight_end":106},{"text":"                    continue;","highlight_start":1,"highlight_end":30},{"text":"                } else {","highlight_start":1,"highlight_end":25},{"text":"                    should_rm_dest_dir = true;","highlight_start":1,"highlight_end":47},{"text":"                }","highlight_start":1,"highlight_end":18},{"text":"            };","highlight_start":1,"highlight_end":15},{"text":"","highlight_start":1,"highlight_end":1},{"text":"            if should_rm_dest_dir {","highlight_start":1,"highlight_end":36},{"text":"                println!(\"removing {}\", dest_dir);","highlight_start":1,"highlight_end":51},{"text":"                if let Err(e) = remove_dir_all(&dest_dir) {","highlight_start":1,"highlight_end":60},{"text":"                    eprintln!(\"Error removing directory {}: {}\", dest_dir, e);","highlight_start":1,"highlight_end":79},{"text":"                }","highlight_start":1,"highlight_end":18},{"text":"                println!(\"creating {}\", dest_dir);","highlight_start":1,"highlight_end":51},{"text":"                if let Err(e) = create_dir_all(&dest_dir) {","highlight_start":1,"highlight_end":60},{"text":"                    eprintln!(\"Error creating directory {}: {}\", dest_dir, e);","highlight_start":1,"highlight_end":79},{"text":"                }","highlight_start":1,"highlight_end":18},{"text":"            }","highlight_start":1,"highlight_end":14},{"text":"        } else {","highlight_start":1,"highlight_end":17},{"text":"            println!(\"creating {}\", dest_dir);","highlight_start":1,"highlight_end":47},{"text":"            if let Err(e) = create_dir_all(&dest_dir) {","highlight_start":1,"highlight_end":56},{"text":"                eprintln!(\"Error creating directory {}: {}\", dest_dir, e);","highlight_start":1,"highlight_end":75},{"text":"            }","highlight_start":1,"highlight_end":14},{"text":"        }","highlight_start":1,"highlight_end":10},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        // ################################################","highlight_start":1,"highlight_end":60},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        // START __inserting_into_dbs__","highlight_start":1,"highlight_end":40},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        let mysql_host = match MYSQLConfig::MYSQL_HOST.value() {","highlight_start":1,"highlight_end":65},{"text":"            MYSQLValue::Str(host) => host,","highlight_start":1,"highlight_end":43},{"text":"            _ => panic!(\"Error getting MYSQL_HOST\"),","highlight_start":1,"highlight_end":53},{"text":"        };","highlight_start":1,"highlight_end":11},{"text":"        let mysql_user = match MYSQLConfig::MYSQL_MASTER.value() {","highlight_start":1,"highlight_end":67},{"text":"            MYSQLValue::Str(user) => user,","highlight_start":1,"highlight_end":43},{"text":"            _ => panic!(\"Error getting MYSQL_MASTER\"),","highlight_start":1,"highlight_end":55},{"text":"        };","highlight_start":1,"highlight_end":11},{"text":"        let mysql_password = match MYSQLConfig::MYSQL_MASTER_PASSWD.value() {","highlight_start":1,"highlight_end":78},{"text":"            MYSQLValue::Str(password) => password,","highlight_start":1,"highlight_end":51},{"text":"            _ => panic!(\"Error getting MYSQL_MASTER_PASSWD\"),","highlight_start":1,"highlight_end":62},{"text":"        };","highlight_start":1,"highlight_end":11},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        let db_opts = OptsBuilder::new()","highlight_start":1,"highlight_end":41},{"text":"            .ip_or_hostname(Some(mysql_host))","highlight_start":1,"highlight_end":46},{"text":"            .user(Some(mysql_user))","highlight_start":1,"highlight_end":36},{"text":"            .pass(Some(mysql_password));","highlight_start":1,"highlight_end":41},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        // drop/create database","highlight_start":1,"highlight_end":32},{"text":"        match Pool::new(db_opts) {","highlight_start":1,"highlight_end":35},{"text":"            Ok(pool) => {","highlight_start":1,"highlight_end":26},{"text":"                match pool.get_conn() {","highlight_start":1,"highlight_end":40},{"text":"                    Ok(mut conn) => {","highlight_start":1,"highlight_end":38},{"text":"                        println!(\"dropping database {}\", database_name);","highlight_start":1,"highlight_end":73},{"text":"                        if let Err(e) = conn.query_drop(format!(\"DROP DATABASE IF EXISTS {};\", database_name)) {","highlight_start":1,"highlight_end":113},{"text":"                            eprintln!(\"Error dropping database {}: {}\", database_name, e);","highlight_start":1,"highlight_end":91},{"text":"                        }","highlight_start":1,"highlight_end":26},{"text":"","highlight_start":1,"highlight_end":1},{"text":"                        println!(\"creating database {}\", database_name);","highlight_start":1,"highlight_end":73},{"text":"                        if let Err(e) = conn.query_drop(format!(\"CREATE DATABASE {};\", database_name)) {","highlight_start":1,"highlight_end":105},{"text":"                            eprintln!(\"Error creating database {}: {}\", database_name, e);","highlight_start":1,"highlight_end":91},{"text":"                        }","highlight_start":1,"highlight_end":26},{"text":"                    }","highlight_start":1,"highlight_end":22},{"text":"                    Err(e) => {","highlight_start":1,"highlight_end":32},{"text":"                        eprintln!(\"Error getting database connection: {}\", e);","highlight_start":1,"highlight_end":79},{"text":"                    }","highlight_start":1,"highlight_end":22},{"text":"                }","highlight_start":1,"highlight_end":18},{"text":"            }","highlight_start":1,"highlight_end":14},{"text":"            Err(e) => {","highlight_start":1,"highlight_end":24},{"text":"                eprintln!(\"Error creating database pool: {}\", e);","highlight_start":1,"highlight_end":66},{"text":"            }","highlight_start":1,"highlight_end":14},{"text":"        }","highlight_start":1,"highlight_end":10},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        // __DB_HANDLING__ end","highlight_start":1,"highlight_end":31},{"text":"    }","highlight_start":1,"highlight_end":6}],"label":"unreachable statement","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/parse-snort.rs","byte_start":13109,"byte_end":13115,"line_start":410,"line_end":410,"column_start":5,"column_end":11,"is_primary":false,"text":[{"text":"    return;","highlight_start":5,"highlight_end":11}],"label":"any code following this expression is unreachable","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unreachable_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unreachable statement\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/parse-snort.rs:412:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m410\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m    return;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12many code following this expression is unreachable\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m411\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m412\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m    for entry in sensor_names_and_instances.iter() {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m413\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let sensor_name = entry.key();\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m414\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let instance = entry.value();\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m504\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    }\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|_____^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33munreachable statement\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unreachable_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `rayon::prelude`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/parse-snort.rs","byte_start":486,"byte_end":500,"line_start":22,"line_end":22,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"use rayon::prelude::*;","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `rayon::prelude`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/parse-snort.rs:22:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m22\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rayon::prelude::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `instance`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/parse-snort.rs","byte_start":13222,"byte_end":13230,"line_start":414,"line_end":414,"column_start":13,"column_end":21,"is_primary":true,"text":[{"text":"        let instance = entry.value();","highlight_start":13,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/bin/parse-snort.rs","byte_start":13222,"byte_end":13230,"line_start":414,"line_end":414,"column_start":13,"column_end":21,"is_primary":true,"text":[{"text":"        let instance = entry.value();","highlight_start":13,"highlight_end":21}],"label":null,"suggested_replacement":"_instance","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `instance`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/parse-snort.rs:414:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m414\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let instance = entry.value();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_instance`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `log_file`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/parse-snort.rs","byte_start":13470,"byte_end":13478,"line_start":418,"line_end":418,"column_start":13,"column_end":21,"is_primary":true,"text":[{"text":"        let log_file          = format!(\"{}/{}.log\", dest_dir, args.log_date);","highlight_start":13,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/bin/parse-snort.rs","byte_start":13470,"byte_end":13478,"line_start":418,"line_end":418,"column_start":13,"column_end":21,"is_primary":true,"text":[{"text":"        let log_file          = format!(\"{}/{}.log\", dest_dir, args.log_date);","highlight_start":13,"highlight_end":21}],"label":null,"suggested_replacement":"_log_file","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `log_file`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/parse-snort.rs:418:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m418\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let log_file          = format!(\"{}/{}.log\", dest_dir, args.log_date);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_log_file`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `should_rm_dest_dir` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/parse-snort.rs","byte_start":13821,"byte_end":13839,"line_start":426,"line_end":426,"column_start":21,"column_end":39,"is_primary":true,"text":[{"text":"            let mut should_rm_dest_dir = false;","highlight_start":21,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`#[warn(unused_assignments)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: value assigned to `should_rm_dest_dir` is never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/parse-snort.rs:426:21\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m426\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            let mut should_rm_dest_dir = false;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_assignments)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `PROGRESS_REPORT_INTERVAL` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/parse-snort.rs","byte_start":1017,"byte_end":1041,"line_start":44,"line_end":44,"column_start":7,"column_end":31,"is_primary":true,"text":[{"text":"const PROGRESS_REPORT_INTERVAL: usize = 1_000_000;  // Report progress every 1M lines","highlight_start":7,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `PROGRESS_REPORT_INTERVAL` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/parse-snort.rs:44:7\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m44\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mconst PROGRESS_REPORT_INTERVAL: usize = 1_000_000;  // Report progress every 1M lines\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"7 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 7 warnings emitted\u001b[0m\n\n"}
