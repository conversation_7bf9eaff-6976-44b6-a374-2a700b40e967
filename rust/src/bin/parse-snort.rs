use std::collections::{HashMap, HashSet};
use std::fs::{File, create_dir_all, remove_dir_all};
use std::io::{<PERSON>ufR<PERSON>, BufReader, Read};
use std::path::Path;
use std::sync::atomic::{AtomicUsize, Ordering};
use std::sync::Arc;
use std::time::Instant;

use clap::Parser;
use dashmap::DashMap;
use memmap2::MmapOptions;
use mysql::{
    OptsBuilder,
    prelude::Queryable,
    Pool,
};
use rayon::iter::{
    IntoParallelRefIterator,
    ParallelIterator,
    IntoParallelIterator,
};
use rayon::prelude::*;
use serde_json;

use eterna::utils_classes::{
    SnortConfig,
    SnortParser,
    MYSQLConfig,
    MYSQLValue,
};

use eterna::utils::{
    create_name_of_database,
};

use eterna::utils_parsers::{
    ConfigType,
    parse_ln,
};

// performance constants for large file processing - optimized for 70GB files
const FILE_BUFFER_SIZE: usize = 64 * 1024 * 1024;  // 64MB buffer for better I/O performance on large files
const MMAP_CHUNK_SIZE: usize = 256 * 1024 * 1024;  // 256MB chunks for memory mapping
const PROGRESS_REPORT_INTERVAL: usize = 1_000_000;  // Report progress every 1M lines

#[derive(Parser, Debug)]
#[command(author, version, about)]
struct Args {
    #[arg(long = "source-log")]
    source_log: String,
    // /FOO/BAR/BAZ/2025-06-13--Fri.log

    #[arg(long = "log-date")]
    log_date: String,
    // 2020-01-02

    #[arg(long = "already-accomplished", num_args = 0..)]
    already_accomplished: Vec<String>,
    // [] OR ["Sensor-1", "Sensor-2", ...]

    #[arg(long = "sensor-list-of-names", num_args = 1..)]
    sensor_list_of_names: Vec<String>,
    // ["Sensor-1", "Sensor-2", "Sensor-3", ...]

    #[arg(long = "sensor-list-of-names-and-addresses", num_args = 1..)]
    sensor_list_of_names_and_addresses: Vec<String>,
    // ["Sensor-1", "***********", "Sensor-2", "***********", ...]

    #[arg(long = "sensor-dict-of-addresses-and-names")]
    sensor_dict_of_addresses_and_names: String,
    // "{\"***********\": \"Sensor-1\", \"***********\": \"Sensor-2\", ...}"

    #[arg(long = "force", num_args = 0)]
    #[arg(default_value_t = false)]
    force: bool,
    // true/false
}

fn trim_newlines(line: &mut String) {
    while line.ends_with('\n') || line.ends_with('\r') {
        line.pop();
    }
}

// Optimized line processing function for memory-mapped files
fn process_mmap_chunk(
    chunk: &[u8],
    start_offset: usize,
    sensor_list_of_names_and_addresses: &[String],
    sensor_dict_of_addresses_and_names: &HashMap<String, String>,
    already_accomplished: &HashSet<String>,
    lines_processed: &Arc<AtomicUsize>,
) -> HashMap<String, Vec<Vec<String>>> {
    let chunk_str = match std::str::from_utf8(chunk) {
        Ok(s) => s,
        Err(_) => {
            eprintln!("Warning: Invalid UTF-8 in chunk starting at offset {}", start_offset);
            return HashMap::new();
        }
    };

    let local_results: HashMap<String, Vec<Vec<String>>> = chunk_str
        .par_lines()
        .enumerate()
        .filter_map(|(line_idx, line)| {
            // Update progress counter
            if line_idx % PROGRESS_REPORT_INTERVAL == 0 {
                lines_processed.fetch_add(PROGRESS_REPORT_INTERVAL, Ordering::Relaxed);
            }

            let line = line.trim();
            if line.is_empty() {
                return None;
            }

            let (sensor_name, parsed_ln) = parse_ln(
                line,
                ConfigType::Snort,
                sensor_list_of_names_and_addresses,
                sensor_dict_of_addresses_and_names,
            );

            // Early exit for already accomplished sensors
            if let Some(ref name) = sensor_name {
                if already_accomplished.contains(name) {
                    return None;
                }
            }

            match (sensor_name, parsed_ln) {
                (Some(name), Some(row)) => Some((name, row)),
                _ => None,
            }
        })
        .fold(HashMap::new, |mut acc, (name, row)| {
            acc.entry(name).or_insert_with(Vec::new).push(row);
            acc
        })
        .reduce(HashMap::new, |mut acc1, acc2| {
            for (k, mut v) in acc2 {
                acc1.entry(k).or_insert_with(Vec::new).append(&mut v);
            }
            acc1
        });

    local_results
}

// Memory-mapped file processing for large files (>1GB)
fn process_with_mmap(
    args: &Args,
    sensor_dict_of_addresses_and_names: &HashMap<String, String>,
    already_accomplished: &HashSet<String>,
    sensor_names_and_instances: &DashMap<String, SnortParser>,
    lines_processed: &Arc<AtomicUsize>,
) {
    let file = File::open(&args.source_log)
        .expect("Failed to open source log for memory mapping");

    let mmap = unsafe {
        MmapOptions::new()
            .map(&file)
            .expect("Failed to create memory map")
    };

    let file_size = mmap.len();
    let num_chunks = (file_size + MMAP_CHUNK_SIZE - 1) / MMAP_CHUNK_SIZE;

    println!("Processing {} chunks of {}MB each", num_chunks, MMAP_CHUNK_SIZE / (1024 * 1024));

    // Process chunks in parallel
    (0..num_chunks).into_par_iter().for_each(|chunk_idx| {
        let start_offset = chunk_idx * MMAP_CHUNK_SIZE;
        let end_offset = std::cmp::min(start_offset + MMAP_CHUNK_SIZE, file_size);

        // Adjust boundaries to avoid splitting lines
        let (adjusted_start, adjusted_end) = if chunk_idx == 0 {
            (0, find_line_boundary(&mmap, end_offset, file_size))
        } else {
            let start = find_line_boundary(&mmap, start_offset, file_size);
            let end = if chunk_idx == num_chunks - 1 {
                file_size
            } else {
                find_line_boundary(&mmap, end_offset, file_size)
            };
            (start, end)
        };

        if adjusted_start >= adjusted_end {
            return;
        }

        let chunk_data = &mmap[adjusted_start..adjusted_end];
        let local_results = process_mmap_chunk(
            chunk_data,
            adjusted_start,
            &args.sensor_list_of_names_and_addresses,
            sensor_dict_of_addresses_and_names,
            already_accomplished,
            lines_processed,
        );

        // Collect results into sensor instances
        for (name, rows) in local_results {
            if let Some(mut instance) = sensor_names_and_instances.get_mut(&name) {
                instance.rows.extend(rows);
            }
        }
    });
}

// Find the next newline boundary to avoid splitting lines
fn find_line_boundary(mmap: &[u8], offset: usize, file_size: usize) -> usize {
    if offset >= file_size {
        return file_size;
    }

    // Look forward for the next newline
    for i in offset..std::cmp::min(offset + 1024, file_size) {
        if mmap[i] == b'\n' {
            return i + 1;
        }
    }

    // If no newline found in the next 1KB, just use the offset
    offset
}

// Buffered reader processing for smaller files
fn process_with_buffered_reader(
    args: &Args,
    sensor_dict_of_addresses_and_names: &HashMap<String, String>,
    already_accomplished: &HashSet<String>,
    sensor_names_and_instances: &DashMap<String, SnortParser>,
    lines_processed: &Arc<AtomicUsize>,
) {
    let file = File::open(&args.source_log)
        .expect("Failed to open source log");
    let mut reader = BufReader::with_capacity(FILE_BUFFER_SIZE, file);

    let pool_chunksize = if let MYSQLValue::Int(size) = MYSQLConfig::POOL_CHUNKSIZE.value() {
        size * 2  // Double the chunk size for better performance
    } else {
        200_000  // Default larger chunk size
    };

    loop {
        let mut chunk = Vec::with_capacity(pool_chunksize);

        // Read chunk of lines
        for _ in 0..pool_chunksize {
            let mut line = String::new();
            match reader.read_line(&mut line) {
                Ok(0) => break, // EOF
                Ok(_) => {
                    trim_newlines(&mut line);
                    chunk.push(line);
                }
                Err(e) => panic!("Error reading line: {}", e),
            }
        }

        if chunk.is_empty() {
            break;
        }

        lines_processed.fetch_add(chunk.len(), Ordering::Relaxed);

        // Process chunk in parallel
        let local_results: HashMap<String, Vec<Vec<String>>> = chunk
            .par_iter()
            .filter_map(|line| {
                let line = line.trim();
                if line.is_empty() {
                    return None;
                }

                let (sensor_name, parsed_ln) = parse_ln(
                    line,
                    ConfigType::Snort,
                    &args.sensor_list_of_names_and_addresses,
                    sensor_dict_of_addresses_and_names,
                );

                if let Some(ref name) = sensor_name {
                    if already_accomplished.contains(name) {
                        return None;
                    }
                }

                match (sensor_name, parsed_ln) {
                    (Some(name), Some(row)) => Some((name, row)),
                    _ => None,
                }
            })
            .fold(HashMap::new, |mut acc, (name, row)| {
                acc.entry(name).or_insert_with(Vec::new).push(row);
                acc
            })
            .reduce(HashMap::new, |mut acc1, acc2| {
                for (k, mut v) in acc2 {
                    acc1.entry(k).or_insert_with(Vec::new).append(&mut v);
                }
                acc1
            });

        // Collect results into sensor instances
        for (name, rows) in local_results {
            if let Some(mut instance) = sensor_names_and_instances.get_mut(&name) {
                instance.rows.extend(rows);
            }
        }
    }
}

fn main() {
    let args = Args::parse();
    let start_time = Instant::now();

    // string -> dict
    let sensor_dict_of_addresses_and_names: HashMap<String, String> =
        serde_json::from_str(&args.sensor_dict_of_addresses_and_names)
            .expect("Failed to parse sensor_dict_of_addresses_and_names");

    // list -> set for O(1) lookup
    let already_accomplished: HashSet<String> =
        args.already_accomplished.into_iter().collect();

    // create dictionary of instances
    let sensor_names_and_instances: DashMap<String, SnortParser> = DashMap::new();
    for s_n in &args.sensor_list_of_names {
        sensor_names_and_instances.insert(
            s_n.clone(),
            SnortParser::new(
                SnortConfig::SLUG.value_string(),
                args.log_date.to_string(),
                s_n.to_string(),
            ),
        );
    }

    // __PARSING__ start
    println!("Starting to parse file: {}", args.source_log);

    // Get file size for progress tracking
    let file_metadata = std::fs::metadata(&args.source_log)
        .expect("Failed to get file metadata");
    let file_size = file_metadata.len();
    println!("File size: {:.2} GB", file_size as f64 / (1024.0 * 1024.0 * 1024.0));

    // Progress tracking
    let lines_processed = Arc::new(AtomicUsize::new(0));
    let lines_processed_clone = Arc::clone(&lines_processed);

    // Spawn progress reporting thread
    let progress_handle = std::thread::spawn(move || {
        let start = Instant::now();
        loop {
            std::thread::sleep(std::time::Duration::from_secs(10));
            let processed = lines_processed_clone.load(Ordering::Relaxed);
            let elapsed = start.elapsed().as_secs_f64();
            let rate = processed as f64 / elapsed;
            println!("Progress: {} lines processed, {:.0} lines/sec", processed, rate);
        }
    });

    // Try memory mapping for better performance on large files
    let use_mmap = file_size > 1024 * 1024 * 1024; // Use mmap for files > 1GB

    if use_mmap {
        println!("Using memory mapping for large file processing...");
        process_with_mmap(
            &args,
            &sensor_dict_of_addresses_and_names,
            &already_accomplished,
            &sensor_names_and_instances,
            &lines_processed,
        );
    } else {
        println!("Using buffered reading for smaller file...");
        process_with_buffered_reader(
            &args,
            &sensor_dict_of_addresses_and_names,
            &already_accomplished,
            &sensor_names_and_instances,
            &lines_processed,
        );
    }

    // Stop progress reporting thread
    drop(progress_handle);

    let parsing_time = start_time.elapsed();
    let total_lines = lines_processed.load(Ordering::Relaxed);

    println!("\nParsing completed!");
    println!("Total time: {:.2} seconds", parsing_time.as_secs_f64());
    println!("Total lines processed: {}", total_lines);
    println!("Average rate: {:.0} lines/sec", total_lines as f64 / parsing_time.as_secs_f64());

    // __TODO__ temporary
    println!("\nLines parsed per sensor:");
    for entry in sensor_names_and_instances.iter() {
        println!("  {}: {} lines", entry.key(), entry.value().rows.len());
    }

    // __PARSING__ end

    // __DB_HANDLING__ start

    for entry in sensor_names_and_instances.iter() {
        let sensor_name = entry.key();
        let instance = entry.value();

        let dest_dir          = format!("{}/{}/{}", SnortConfig::get_logs_parsed_dir(), sensor_name, args.log_date);
        let accomplished_file = format!("{}/{}-accomplished.log", dest_dir, args.log_date);
        let log_file          = format!("{}/{}.log", dest_dir, args.log_date);

        let database_name = create_name_of_database(&SnortConfig::SLUG.value_string(), &args.log_date, sensor_name);

        // ################################################

        // remove and/or create dest_dir
        if Path::new(&dest_dir).exists() {
            let mut should_rm_dest_dir = false;

            if args.force {
                should_rm_dest_dir = true;
            } else {
                if Path::new(&accomplished_file).exists() {
                    println!("{} for sensor {} is already parsed. skipping", args.log_date, sensor_name);
                    continue;
                } else {
                    should_rm_dest_dir = true;
                }
            };

            if should_rm_dest_dir {
                println!("removing {}", dest_dir);
                if let Err(e) = remove_dir_all(&dest_dir) {
                    eprintln!("Error removing directory {}: {}", dest_dir, e);
                }
                println!("creating {}", dest_dir);
                if let Err(e) = create_dir_all(&dest_dir) {
                    eprintln!("Error creating directory {}: {}", dest_dir, e);
                }
            }
        } else {
            println!("creating {}", dest_dir);
            if let Err(e) = create_dir_all(&dest_dir) {
                eprintln!("Error creating directory {}: {}", dest_dir, e);
            }
        }

        // ################################################

        // START __inserting_into_dbs__

        let mysql_host = match MYSQLConfig::MYSQL_HOST.value() {
            MYSQLValue::Str(host) => host,
            _ => panic!("Error getting MYSQL_HOST"),
        };
        let mysql_user = match MYSQLConfig::MYSQL_MASTER.value() {
            MYSQLValue::Str(user) => user,
            _ => panic!("Error getting MYSQL_MASTER"),
        };
        let mysql_password = match MYSQLConfig::MYSQL_MASTER_PASSWD.value() {
            MYSQLValue::Str(password) => password,
            _ => panic!("Error getting MYSQL_MASTER_PASSWD"),
        };

        let db_opts = OptsBuilder::new()
            .ip_or_hostname(Some(mysql_host))
            .user(Some(mysql_user))
            .pass(Some(mysql_password));

        // drop/create database
        match Pool::new(db_opts) {
            Ok(pool) => {
                match pool.get_conn() {
                    Ok(mut conn) => {
                        println!("dropping database {}", database_name);
                        if let Err(e) = conn.query_drop(format!("DROP DATABASE IF EXISTS {};", database_name)) {
                            eprintln!("Error dropping database {}: {}", database_name, e);
                        }

                        println!("creating database {}", database_name);
                        if let Err(e) = conn.query_drop(format!("CREATE DATABASE {};", database_name)) {
                            eprintln!("Error creating database {}: {}", database_name, e);
                        }
                    }
                    Err(e) => {
                        eprintln!("Error getting database connection: {}", e);
                    }
                }
            }
            Err(e) => {
                eprintln!("Error creating database pool: {}", e);
            }
        }

        // __DB_HANDLING__ end
    }










    println!("\nParser Finished Successfully");
}
